#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SYSTÈME DE CACHE INTELLIGENT POUR DATASET BACCARAT
Évite le rechargement multiple du fichier JSON volumineux
"""

import json
import os
import time
from typing import Dict, Any, Optional

class DatasetCache:
    """Cache intelligent pour le dataset baccarat"""
    
    def __init__(self):
        self._cache = {}
        self._cache_timestamps = {}
        self._cache_enabled = True
    
    def get_dataset(self, fichier_json: str, force_reload: bool = False) -> Optional[Dict[str, Any]]:
        """
        Récupère le dataset avec mise en cache intelligente
        
        Args:
            fichier_json (str): Chemin vers le fichier JSON
            force_reload (bool): Force le rechargement même si en cache
            
        Returns:
            Dict contenant le dataset ou None si erreur
        """
        if not self._cache_enabled:
            return self._load_dataset_direct(fichier_json)
        
        # Vérifier si le fichier existe
        if not os.path.exists(fichier_json):
            print(f"❌ Fichier non trouvé: {fichier_json}")
            return None
        
        # Obtenir les informations du fichier
        file_stat = os.stat(fichier_json)
        file_mtime = file_stat.st_mtime
        file_size = file_stat.st_size
        
        cache_key = os.path.abspath(fichier_json)
        
        # Vérifier si le fichier est en cache et à jour
        if (not force_reload and 
            cache_key in self._cache and 
            cache_key in self._cache_timestamps and
            self._cache_timestamps[cache_key] >= file_mtime):
            
            print(f"📋 CACHE HIT: Utilisation du cache pour {os.path.basename(fichier_json)}")
            print(f"   Taille: {file_size:,} octets")
            print(f"   Parties en cache: {len(self._cache[cache_key]):,}")
            return self._cache[cache_key]
        
        # Charger le fichier et le mettre en cache
        print(f"📥 CACHE MISS: Chargement de {os.path.basename(fichier_json)}")
        print(f"   Taille: {file_size:,} octets")
        
        start_time = time.time()
        dataset = self._load_dataset_direct(fichier_json)
        load_time = time.time() - start_time
        
        if dataset:
            # Mettre en cache
            self._cache[cache_key] = dataset
            self._cache_timestamps[cache_key] = file_mtime
            
            print(f"✅ Dataset chargé et mis en cache en {load_time:.2f}s")
            print(f"   Parties chargées: {len(dataset):,}")
            print(f"   Mémoire cache: {self._estimate_cache_size():,} octets")
        
        return dataset
    
    def _load_dataset_direct(self, fichier_json: str) -> Optional[Dict[str, Any]]:
        """Charge directement le dataset depuis le fichier"""
        try:
            with open(fichier_json, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Erreur chargement {fichier_json}: {e}")
            return None
    
    def _estimate_cache_size(self) -> int:
        """Estime la taille du cache en mémoire"""
        import sys
        total_size = 0
        for dataset in self._cache.values():
            total_size += sys.getsizeof(dataset)
        return total_size
    
    def clear_cache(self):
        """Vide le cache"""
        self._cache.clear()
        self._cache_timestamps.clear()
        print("🗑️ Cache vidé")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Retourne les informations du cache"""
        return {
            'nb_fichiers_en_cache': len(self._cache),
            'taille_estimee': self._estimate_cache_size(),
            'fichiers': list(self._cache.keys())
        }
    
    def disable_cache(self):
        """Désactive le cache"""
        self._cache_enabled = False
        print("⚠️ Cache désactivé")
    
    def enable_cache(self):
        """Active le cache"""
        self._cache_enabled = True
        print("✅ Cache activé")

# Instance globale du cache
_dataset_cache = DatasetCache()

def get_dataset_cached(fichier_json: str, force_reload: bool = False) -> Optional[Dict[str, Any]]:
    """
    Fonction utilitaire pour récupérer un dataset avec cache
    
    Args:
        fichier_json (str): Chemin vers le fichier JSON
        force_reload (bool): Force le rechargement
        
    Returns:
        Dict contenant le dataset ou None si erreur
    """
    return _dataset_cache.get_dataset(fichier_json, force_reload)

def clear_dataset_cache():
    """Vide le cache global"""
    _dataset_cache.clear_cache()

def get_cache_info() -> Dict[str, Any]:
    """Retourne les informations du cache global"""
    return _dataset_cache.get_cache_info()

def disable_dataset_cache():
    """Désactive le cache global"""
    _dataset_cache.disable_cache()

def enable_dataset_cache():
    """Active le cache global"""
    _dataset_cache.enable_cache()

if __name__ == "__main__":
    # Test du système de cache
    print("🧪 TEST DU SYSTÈME DE CACHE")
    print("=" * 50)
    
    fichier_test = "dataset_baccarat_lupasco_20250623_080828.json"
    
    if os.path.exists(fichier_test):
        # Premier chargement
        print("\n1️⃣ PREMIER CHARGEMENT:")
        dataset1 = get_dataset_cached(fichier_test)
        
        # Deuxième chargement (doit utiliser le cache)
        print("\n2️⃣ DEUXIÈME CHARGEMENT:")
        dataset2 = get_dataset_cached(fichier_test)
        
        # Vérifier que c'est le même objet
        print(f"\n🔍 VÉRIFICATION:")
        print(f"   Même objet en mémoire: {dataset1 is dataset2}")
        print(f"   Parties identiques: {len(dataset1) == len(dataset2) if dataset1 and dataset2 else False}")
        
        # Informations du cache
        print(f"\n📊 INFORMATIONS CACHE:")
        info = get_cache_info()
        for key, value in info.items():
            print(f"   {key}: {value}")
    else:
        print(f"❌ Fichier de test non trouvé: {fichier_test}")
