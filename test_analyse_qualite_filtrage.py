#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
================================================================================
                    TEST ANALYSE QUALITÉ FILTRAGE L5
================================================================================

Script de test pour analyser la qualité du filtrage L5 en vérifiant
si les résultats réels sont contenus dans les simulations gardées.

Auteur: Expert Statisticien
Date: 2025-06-23
================================================================================
"""

import sys
import os
import json
from typing import List, Dict

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from predicteur_temps_reel_ultra import PredicteurTempsReelUltra, EtatPartieActuel, AnalyseQualiteFiltrage

def tester_analyse_qualite_filtrage(nb_parties_max: int = 100):
    """
    Test de l'analyse de qualité du filtrage L5
    
    Args:
        nb_parties_max: Nombre maximum de parties à analyser
    """
    print("🧪 TEST ANALYSE QUALITÉ FILTRAGE L5")
    print("=" * 60)
    
    # Charger les données
    try:
        with open('dataset_baccarat_lupasco_20250623_080828.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        if isinstance(data, dict) and 'parties' in data:
            parties = data['parties']
        elif isinstance(data, list):
            parties = data
        else:
            parties = [data]
            
        print(f"✅ {len(parties)} parties chargées")
        
    except Exception as e:
        print(f"❌ Erreur chargement données: {e}")
        return False
    
    # Créer le prédicteur
    predicteur = PredicteurTempsReelUltra()
    
    # Statistiques globales
    nb_parties_analysees = 0
    nb_analyses_reussies = 0
    
    # Statistiques de qualité
    nb_resultat_dans_gardees = 0
    nb_resultat_dans_exclues = 0
    nb_resultat_tie = 0
    nb_wait_l5 = 0
    nb_wait_egalite = 0
    
    # Détails par type de résultat
    stats_par_resultat = {
        'BANKER': {'total': 0, 'dans_gardees': 0, 'dans_exclues': 0},
        'PLAYER': {'total': 0, 'dans_gardees': 0, 'dans_exclues': 0},
        'TIE': {'total': 0, 'dans_gardees': 0, 'dans_exclues': 0}
    }
    
    # Analyser les parties
    for i, partie in enumerate(parties[:nb_parties_max]):
        if i % 20 == 0:
            print(f"📊 Analyse partie {i+1}/{nb_parties_max}")
            
        try:
            mains = partie.get('mains', [])
            if len(mains) < 10:  # Besoin d'au moins 10 mains
                continue
                
            nb_parties_analysees += 1
            
            # Analyser la main 9 (index 8) pour prédire la main 10
            main_a_analyser = 8  # Main 9 (0-indexé)
            
            # Construire l'état de la partie jusqu'à la main 9
            sequence_index5 = []
            sequence_index1 = []
            
            for j in range(main_a_analyser):
                main = mains[j]
                resultat = main.get('resultat', 'BANKER')
                # Simplifier INDEX5 pour le test
                index5 = f"0_A_{resultat}"
                index1 = resultat
                
                sequence_index5.append(index5)
                sequence_index1.append(index1)
            
            # Créer l'état de la partie
            etat_partie = EtatPartieActuel(
                sequence_index5=sequence_index5,
                sequence_index1=sequence_index1,
                main_actuelle=main_a_analyser + 1,
                ratio_l4_actuel=0.5,  # Sera recalculé
                ratio_l5_actuel=0.5,  # Sera recalculé
                ratio_l4_precedent=0.5,
                ratio_l5_precedent=0.5
            )
            
            # Obtenir le résultat réel de la main 10
            if main_a_analyser >= len(mains):
                continue
                
            resultat_reel = mains[main_a_analyser].get('resultat', 'BANKER')
            
            # Analyser la qualité du filtrage
            analyse = predicteur.analyser_qualite_filtrage_main(etat_partie, resultat_reel)
            nb_analyses_reussies += 1
            
            # Compter les statistiques
            stats_par_resultat[resultat_reel]['total'] += 1
            
            if analyse.type_prediction == 'WAIT_L5':
                nb_wait_l5 += 1
            elif analyse.type_prediction == 'WAIT_EGALITE':
                nb_wait_egalite += 1
            else:
                # Analyser où se trouve le résultat
                if resultat_reel == 'TIE':
                    nb_resultat_tie += 1
                    stats_par_resultat['TIE']['total'] += 1
                else:
                    if analyse.resultat_dans_gardees:
                        nb_resultat_dans_gardees += 1
                        stats_par_resultat[resultat_reel]['dans_gardees'] += 1
                    
                    if analyse.resultat_dans_exclues:
                        nb_resultat_dans_exclues += 1
                        stats_par_resultat[resultat_reel]['dans_exclues'] += 1
            
            # Afficher quelques exemples détaillés
            if i < 5:
                print(f"\n📋 EXEMPLE {i+1}:")
                print(f"   Main: {analyse.numero_main}")
                print(f"   Résultat réel: {analyse.resultat_reel}")
                print(f"   Type prédiction: {analyse.type_prediction}")
                print(f"   Simulations gardées: {analyse.nb_simulations_gardees}")
                print(f"   Simulations exclues: {analyse.nb_simulations_exclues}")
                print(f"   Résultat dans gardées: {analyse.resultat_dans_gardees}")
                print(f"   Résultat dans exclues: {analyse.resultat_dans_exclues}")
                
        except Exception as e:
            print(f"⚠️ Erreur partie {i+1}: {e}")
            continue
    
    # Afficher les résultats finaux
    print("\n" + "=" * 80)
    print("🎯 RÉSULTATS ANALYSE QUALITÉ FILTRAGE L5")
    print("=" * 80)
    
    print(f"\n📊 STATISTIQUES GLOBALES:")
    print(f"   Parties analysées: {nb_parties_analysees}")
    print(f"   Analyses réussies: {nb_analyses_reussies}")
    print(f"   WAIT L5 (aucune simulation gardée): {nb_wait_l5}")
    print(f"   WAIT égalité: {nb_wait_egalite}")
    
    total_analysable = nb_resultat_dans_gardees + nb_resultat_dans_exclues
    
    if total_analysable > 0:
        print(f"\n🎯 QUALITÉ DU FILTRAGE L5:")
        print(f"   Résultats réels dans simulations GARDÉES: {nb_resultat_dans_gardees}")
        print(f"   Résultats réels dans simulations EXCLUES: {nb_resultat_dans_exclues}")
        print(f"   Résultats réels TIE: {nb_resultat_tie}")
        
        taux_gardees = nb_resultat_dans_gardees / total_analysable * 100
        taux_exclues = nb_resultat_dans_exclues / total_analysable * 100
        
        print(f"\n📈 PROPORTIONS:")
        print(f"   Taux résultat dans GARDÉES: {taux_gardees:.1f}%")
        print(f"   Taux résultat dans EXCLUES: {taux_exclues:.1f}%")
        
        if taux_gardees > taux_exclues:
            print(f"   ✅ FILTRAGE EFFICACE: Plus de résultats dans les gardées")
        else:
            print(f"   ❌ FILTRAGE PROBLÉMATIQUE: Plus de résultats dans les exclues")
    
    print(f"\n📋 ANALYSE PAR TYPE DE RÉSULTAT:")
    for type_resultat, stats in stats_par_resultat.items():
        if stats['total'] > 0:
            taux_gardees = stats['dans_gardees'] / stats['total'] * 100 if stats['total'] > 0 else 0
            taux_exclues = stats['dans_exclues'] / stats['total'] * 100 if stats['total'] > 0 else 0
            print(f"   {type_resultat}: {stats['total']} total, "
                  f"{stats['dans_gardees']} gardées ({taux_gardees:.1f}%), "
                  f"{stats['dans_exclues']} exclues ({taux_exclues:.1f}%)")
    
    return True


if __name__ == "__main__":
    # Test avec 100 parties
    tester_analyse_qualite_filtrage(100)
