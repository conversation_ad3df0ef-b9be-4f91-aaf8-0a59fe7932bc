#!/usr/bin/env python3
"""
TEST IMPACT EPSILON SUR CALCUL ENTROPIE
Analyse quantitative de l'erreur introduite par l'ajout de 1e-10
"""

import numpy as np
from collections import Counter
import matplotlib.pyplot as plt

def entropie_correcte(sequence):
    """
    Calcul d'entropie mathématiquement correct
    H(X) = -Σ p(x) log₂ p(x) pour p(x) > 0 seulement
    """
    counts = Counter(sequence)
    total = len(sequence)
    
    entropie = 0.0
    for count in counts.values():
        if count > 0:  # Condition mathématiquement correcte
            p = count / total
            entropie -= p * np.log2(p)
    
    return entropie

def entropie_avec_epsilon(sequence, epsilon=1e-10):
    """
    Calcul d'entropie avec epsilon (INCORRECT)
    Simule l'erreur du code original
    """
    counts = np.bincount(sequence, minlength=18)
    probs = counts / len(sequence)
    return -np.sum(probs * np.log2(probs + epsilon))

def analyser_impact_epsilon():
    """Analyse l'impact de l'epsilon sur différents types de distributions"""
    
    print("ANALYSE IMPACT EPSILON SUR ENTROPIE DE SHANNON")
    print("=" * 60)
    
    # Test 1: Distribution très concentrée (cas critique)
    sequence_concentree = [0] * 95 + [1] * 5  # 95% vs 5%
    
    entropie_vraie = entropie_correcte(sequence_concentree)
    entropie_epsilon = entropie_avec_epsilon(np.array(sequence_concentree))
    erreur_relative = abs(entropie_epsilon - entropie_vraie) / entropie_vraie * 100
    
    print(f"\n1. DISTRIBUTION CONCENTRÉE (95%-5%)")
    print(f"   Entropie correcte    : {entropie_vraie:.6f} bits")
    print(f"   Entropie avec epsilon: {entropie_epsilon:.6f} bits")
    print(f"   Erreur relative      : {erreur_relative:.3f}%")
    print(f"   Impact               : {'CRITIQUE' if erreur_relative > 1 else 'MODÉRÉ'}")
    
    # Test 2: Distribution uniforme partielle
    sequence_partielle = [0, 1, 2, 3, 4] * 20  # 5 valeurs sur 18 possibles
    
    entropie_vraie = entropie_correcte(sequence_partielle)
    entropie_epsilon = entropie_avec_epsilon(np.array(sequence_partielle))
    erreur_relative = abs(entropie_epsilon - entropie_vraie) / entropie_vraie * 100
    
    print(f"\n2. DISTRIBUTION PARTIELLE (5/18 valeurs)")
    print(f"   Entropie correcte    : {entropie_vraie:.6f} bits")
    print(f"   Entropie avec epsilon: {entropie_epsilon:.6f} bits")
    print(f"   Erreur relative      : {erreur_relative:.3f}%")
    print(f"   Impact               : {'CRITIQUE' if erreur_relative > 1 else 'MODÉRÉ'}")
    
    # Test 3: Distribution uniforme complète
    sequence_uniforme = list(range(18)) * 10  # Toutes les 18 valeurs
    
    entropie_vraie = entropie_correcte(sequence_uniforme)
    entropie_epsilon = entropie_avec_epsilon(np.array(sequence_uniforme))
    erreur_relative = abs(entropie_epsilon - entropie_vraie) / entropie_vraie * 100
    
    print(f"\n3. DISTRIBUTION UNIFORME COMPLÈTE (18/18 valeurs)")
    print(f"   Entropie correcte    : {entropie_vraie:.6f} bits")
    print(f"   Entropie avec epsilon: {entropie_epsilon:.6f} bits")
    print(f"   Erreur relative      : {erreur_relative:.3f}%")
    print(f"   Impact               : {'CRITIQUE' if erreur_relative > 1 else 'NÉGLIGEABLE'}")
    
    # Test 4: Cas extrême - une seule valeur
    sequence_extreme = [0] * 100
    
    entropie_vraie = entropie_correcte(sequence_extreme)
    entropie_epsilon = entropie_avec_epsilon(np.array(sequence_extreme))
    
    print(f"\n4. CAS EXTRÊME (une seule valeur)")
    print(f"   Entropie correcte    : {entropie_vraie:.6f} bits")
    print(f"   Entropie avec epsilon: {entropie_epsilon:.6f} bits")
    print(f"   Différence absolue   : {abs(entropie_epsilon - entropie_vraie):.6f} bits")
    print(f"   Impact               : CATASTROPHIQUE")
    
    return {
        'concentree': (entropie_vraie, entropie_epsilon, erreur_relative),
        'partielle': entropie_vraie,
        'uniforme': entropie_vraie,
        'extreme': (entropie_vraie, entropie_epsilon)
    }

def analyser_impact_par_epsilon():
    """Analyse l'impact selon différentes valeurs d'epsilon"""
    
    print(f"\n\nANALYSE IMPACT SELON VALEUR EPSILON")
    print("=" * 50)
    
    sequence_test = [0] * 90 + [1] * 10  # Distribution 90%-10%
    entropie_vraie = entropie_correcte(sequence_test)
    
    epsilons = [1e-15, 1e-12, 1e-10, 1e-8, 1e-6, 1e-4]
    
    print(f"Distribution test: 90%-10%")
    print(f"Entropie correcte: {entropie_vraie:.6f} bits")
    print(f"\nImpact selon epsilon:")
    
    for eps in epsilons:
        entropie_eps = entropie_avec_epsilon(np.array(sequence_test), eps)
        erreur = abs(entropie_eps - entropie_vraie) / entropie_vraie * 100
        print(f"   ε = {eps:8.0e} → Erreur: {erreur:6.3f}%")

if __name__ == "__main__":
    resultats = analyser_impact_epsilon()
    analyser_impact_par_epsilon()
    
    print(f"\n\nCONCLUSION STATISTIQUE:")
    print("=" * 40)
    print("L'ajout de 1e-10 introduit des erreurs systématiques")
    print("particulièrement critiques pour les distributions concentrées.")
    print("La solution mathématiquement correcte est d'utiliser:")
    print("H(X) = -Σ p(x) log₂ p(x) SEULEMENT pour p(x) > 0")
