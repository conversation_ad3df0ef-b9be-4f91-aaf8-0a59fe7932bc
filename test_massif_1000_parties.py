#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Massif du Prédicteur sur 1000 Parties Réelles
Évalue la performance du prédicteur sans métriques DIFF

Auteur: Expert Statisticien
Date: 2025-06-23
"""

import sys
import os
import json
import time
from typing import List, Dict, Tuple
from dataclasses import dataclass

# Ajouter le répertoire courant au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

@dataclass
class ResultatPrediction:
    """Résultat d'une prédiction individuelle RÉVOLUTIONNAIRE"""
    main_numero: int
    prediction: str
    resultat_reel: str
    statut: str  # 'SUCCÈS', 'ÉCHEC', 'NEUTRE' (TIE), 'WAIT', 'ABSTENTION'
    probabilite: float
    confiance: str
    nb_simulations: int
    temps_calcul: float

    # NOUVEAUX CHAMPS RÉVOLUTIONNAIRES
    strategie_utilisee: str = "DEFAUT"
    performance_theorique: float = 50.0
    niveau_efficacite: str = "AUCUNE"
    ecart_predictif: float = 0.0
    prediction_so: str = "DEFAUT"
    justification: str = ""

@dataclass
class StatistiquesGlobales:
    """Statistiques globales du test RÉVOLUTIONNAIRE"""
    nb_predictions_total: int
    nb_succes: int
    nb_echecs: int
    nb_neutres: int  # TIE réels
    nb_wait: int
    nb_abstentions: int
    taux_succes: float
    taux_echec: float
    temps_total: float
    temps_moyen_prediction: float

    # NOUVELLES STATISTIQUES RÉVOLUTIONNAIRES
    nb_predictions_s: int = 0  # Nombre de prédictions continuation
    nb_predictions_o: int = 0  # Nombre de prédictions alternance
    taux_succes_s: float = 0.0  # Taux de succès pour continuation
    taux_succes_o: float = 0.0  # Taux de succès pour alternance
    performance_moyenne: float = 0.0  # Performance théorique moyenne
    ecart_predictif_moyen: float = 0.0  # Écart prédictif moyen
    strategie_plus_efficace: str = ""  # Stratégie la plus efficace
    nb_strategies_utilisees: int = 0  # Nombre de stratégies différentes utilisées

def charger_dataset_baccarat(fichier: str, nb_parties: int = 1000) -> List[Dict]:
    """
    Charge le dataset baccarat et retourne les premières parties

    Args:
        fichier: Chemin vers le fichier JSON
        nb_parties: Nombre de parties à charger

    Returns:
        List[Dict]: Liste des parties chargées
    """
    try:
        print(f"📂 Chargement du dataset: {fichier}")
        print(f"⚠️ Chargement de {nb_parties} parties (peut prendre du temps pour gros fichiers)")

        with open(fichier, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Structure identifiée: {"metadata": ..., "configuration": ..., "parties": [...]}
        if isinstance(data, dict) and 'parties' in data:
            parties = data['parties'][:nb_parties]
            print(f"✅ Format dict détecté - {len(data['parties'])} parties disponibles")
        elif isinstance(data, list):
            parties = data[:nb_parties]
            print(f"✅ Format liste détecté - {len(data)} parties disponibles")
        else:
            print(f"❌ Format de dataset non reconnu: {type(data)}")
            if isinstance(data, dict):
                print(f"   Clés disponibles: {list(data.keys())}")
            return []

        print(f"✅ {len(parties)} parties chargées pour le test")
        return parties

    except FileNotFoundError:
        print(f"❌ Fichier non trouvé: {fichier}")
        return []
    except json.JSONDecodeError as e:
        print(f"❌ Erreur JSON: {e}")
        return []
    except MemoryError:
        print(f"❌ Erreur mémoire - Fichier trop volumineux")
        print(f"💡 Essayez avec un nombre de parties plus petit")
        return []
    except Exception as e:
        print(f"❌ Erreur chargement: {e}")
        return []

def convertir_partie_vers_etat(partie: Dict, main_actuelle: int):
    """
    Convertit une partie du dataset vers un état prédicteur

    Args:
        partie: Partie du dataset
        main_actuelle: Numéro de la main actuelle

    Returns:
        EtatPartieActuel: État pour le prédicteur
    """
    from predicteur_temps_reel_ultra import EtatPartieActuel

    # Extraire les mains jusqu'à main_actuelle
    mains = partie.get('mains', [])[:main_actuelle]

    if len(mains) < 6:  # Minimum pour calculs L4/L5
        return None

    # Convertir vers INDEX5 et INDEX1 selon la structure identifiée
    sequence_index5 = []
    sequence_index1 = []

    for main in mains:
        # Gestion flexible de différents formats de dataset
        index5_value = None
        index1_value = None

        # Format 1: Structure du fichier test (index5_combined, index3_result)
        if 'index5_combined' in main:
            index5_value = main['index5_combined']
        elif 'index5' in main:
            index5_value = main['index5']

        if 'index3_result' in main:
            index1_value = main['index3_result']
        elif 'resultat' in main:
            index1_value = main['resultat']
        elif 'index1' in main:
            index1_value = main['index1']
        elif 'result' in main:
            index1_value = main['result']

        # Ajouter seulement si les deux valeurs sont trouvées
        if index5_value and index1_value:
            sequence_index5.append(index5_value)
            sequence_index1.append(index1_value)

    if len(sequence_index5) != len(sequence_index1) or len(sequence_index5) == 0:
        return None

    # UTILISER LES CAPACITÉS INTÉGRÉES DU PRÉDICTEUR
    # Le prédicteur calcule lui-même les ratios, on donne des valeurs par défaut
    return EtatPartieActuel(
        sequence_index5=sequence_index5,
        sequence_index1=sequence_index1,
        main_actuelle=len(sequence_index5),
        ratio_l4_actuel=0.5,  # Valeur par défaut - le prédicteur recalculera
        ratio_l5_actuel=0.6,  # Valeur par défaut - le prédicteur recalculera
        ratio_l4_precedent=0.4,  # Valeur par défaut
        ratio_l5_precedent=0.5   # Valeur par défaut
    )

def evaluer_prediction(prediction: str, resultat_reel: str) -> str:
    """
    Évalue une prédiction selon les règles définies avec validation stratégique

    Args:
        prediction: Prédiction du système
        resultat_reel: Résultat réel de la main (BANKER/PLAYER/TIE)

    Returns:
        str: 'SUCCÈS', 'ÉCHEC', 'NEUTRE', 'WAIT', 'ABSTENTION'
    """
    # WAIT est maintenant neutre (ni succès ni échec) selon les nouvelles règles
    if prediction == 'WAIT':
        return 'NEUTRE'  # Changé de 'WAIT' à 'NEUTRE'

    if prediction == 'ABSTENTION':
        return 'NEUTRE'  # Changé de 'ABSTENTION' à 'NEUTRE'

    # Si le résultat réel est TIE → NEUTRE (ni succès ni échec)
    if resultat_reel == 'TIE':
        return 'NEUTRE'

    # Comparaison directe pour BANKER/PLAYER
    # Les valeurs possibles identifiées sont: "BANKER", "PLAYER", "TIE"
    if prediction == resultat_reel:
        return 'SUCCÈS'
    else:
        return 'ÉCHEC'

def tester_predicteur_massif(nb_parties: int = 1000, mode_strategies_elites: bool = False) -> StatistiquesGlobales:
    """
    Test massif du prédicteur sur nb_parties

    Args:
        nb_parties: Nombre de parties à tester
        mode_strategies_elites: Si True, utilise uniquement les stratégies les plus performantes

    Returns:
        StatistiquesGlobales: Résultats du test
    """
    mode_text = "STRATÉGIES ÉLITES" if mode_strategies_elites else "TOUTES STRATÉGIES"
    print(f"🚀 DÉBUT TEST MASSIF SUR {nb_parties} PARTIES - MODE {mode_text}")
    print("=" * 70)
    
    # Charger le dataset principal (utiliser toutes les parties disponibles)
    parties = charger_dataset_baccarat('dataset_baccarat_lupasco_20250623_080828.json', nb_parties)
    if not parties:
        print("❌ Impossible de charger le dataset")
        return None
    
    # Initialiser le prédicteur
    try:
        from predicteur_temps_reel_ultra import PredicteurTempsReelUltra
        predicteur = PredicteurTempsReelUltra(mode_strategies_elites=mode_strategies_elites)
        print("✅ Prédicteur initialisé")
    except Exception as e:
        print(f"❌ Erreur initialisation prédicteur: {e}")
        return None
    
    # Variables de suivi
    resultats = []
    temps_debut = time.time()
    
    nb_succes = 0
    nb_echecs = 0
    nb_neutres = 0
    nb_wait = 0
    nb_abstentions = 0
    nb_predictions_valides = 0
    
    print(f"\n📊 TRAITEMENT DES PARTIES:")
    
    for i, partie in enumerate(parties):
        try:
            # Prendre une main au milieu de la partie pour avoir assez d'historique
            mains_partie = partie.get('mains', [])
            if len(mains_partie) < 10:  # Minimum pour test valide
                continue
            
            # Tester sur la main 8 (index 7) pour prédire la main 9
            main_test = 8
            if len(mains_partie) <= main_test:
                continue
            
            # Convertir vers état prédicteur
            etat = convertir_partie_vers_etat(partie, main_test)
            if etat is None:
                continue
            
            # Obtenir le résultat réel de la main suivante (gestion flexible)
            main_suivante = mains_partie[main_test]
            resultat_reel = None

            # Essayer différents noms de champs
            for champ in ['index3_result', 'resultat', 'result', 'index1']:
                if champ in main_suivante:
                    resultat_reel = main_suivante[champ]
                    break

            if not resultat_reel or resultat_reel == 'UNKNOWN':
                continue
            
            # Effectuer la prédiction
            temps_pred_debut = time.time()
            try:
                prediction_obj = predicteur.predire_main_suivante(etat)
                temps_pred = time.time() - temps_pred_debut
                
                # Évaluer la prédiction
                statut = evaluer_prediction(prediction_obj.prediction_finale, resultat_reel)
                
                # Compter les résultats (WAIT et ABSTENTION sont maintenant NEUTRE)
                if statut == 'SUCCÈS':
                    nb_succes += 1
                elif statut == 'ÉCHEC':
                    nb_echecs += 1
                elif statut == 'NEUTRE':
                    nb_neutres += 1
                    # Distinguer les types de neutres pour information
                    if prediction_obj.prediction_finale == 'WAIT':
                        nb_wait += 1
                    elif prediction_obj.prediction_finale == 'ABSTENTION':
                        nb_abstentions += 1
                
                nb_predictions_valides += 1
                
                # Stocker le résultat RÉVOLUTIONNAIRE
                resultats.append(ResultatPrediction(
                    main_numero=main_test + 1,
                    prediction=prediction_obj.prediction_finale,
                    resultat_reel=resultat_reel,
                    statut=statut,
                    probabilite=prediction_obj.probabilite,
                    confiance=prediction_obj.confiance,
                    nb_simulations=prediction_obj.nb_simulations_total,
                    temps_calcul=temps_pred,
                    # NOUVEAUX CHAMPS RÉVOLUTIONNAIRES
                    strategie_utilisee=prediction_obj.strategie_utilisee,
                    performance_theorique=prediction_obj.performance_theorique,
                    niveau_efficacite=prediction_obj.niveau_efficacite,
                    ecart_predictif=prediction_obj.ecart_predictif,
                    prediction_so=prediction_obj.prediction_so,
                    justification=prediction_obj.justification
                ))
                
                # Affichage périodique
                if (i + 1) % 100 == 0:
                    print(f"   Partie {i+1:4d}/{nb_parties} - Prédictions valides: {nb_predictions_valides}")
                
            except Exception as e:
                print(f"⚠️ Erreur prédiction partie {i+1}: {e}")
                continue
                
        except Exception as e:
            print(f"⚠️ Erreur traitement partie {i+1}: {e}")
            continue
    
    temps_total = time.time() - temps_debut

    # Calculer les statistiques RÉVOLUTIONNAIRES
    if nb_predictions_valides > 0:
        # Calculer taux sur prédictions non-neutres uniquement
        nb_predictions_decisives = nb_succes + nb_echecs
        taux_succes = (nb_succes / nb_predictions_decisives * 100) if nb_predictions_decisives > 0 else 0
        taux_echec = (nb_echecs / nb_predictions_decisives * 100) if nb_predictions_decisives > 0 else 0
        temps_moyen = temps_total / nb_predictions_valides

        # NOUVELLES STATISTIQUES RÉVOLUTIONNAIRES
        # Analyser les prédictions S vs O
        resultats_s = [r for r in resultats if r.prediction_so == 'S' and r.statut in ['SUCCÈS', 'ÉCHEC']]
        resultats_o = [r for r in resultats if r.prediction_so == 'O' and r.statut in ['SUCCÈS', 'ÉCHEC']]

        nb_predictions_s = len(resultats_s)
        nb_predictions_o = len(resultats_o)

        # Taux de succès pour S et O
        succes_s = len([r for r in resultats_s if r.statut == 'SUCCÈS'])
        succes_o = len([r for r in resultats_o if r.statut == 'SUCCÈS'])

        taux_succes_s = (succes_s / nb_predictions_s * 100) if nb_predictions_s > 0 else 0
        taux_succes_o = (succes_o / nb_predictions_o * 100) if nb_predictions_o > 0 else 0

        # Performance moyenne et écart prédictif moyen
        performances = [r.performance_theorique for r in resultats if r.statut in ['SUCCÈS', 'ÉCHEC']]
        ecarts = [r.ecart_predictif for r in resultats if r.statut in ['SUCCÈS', 'ÉCHEC']]

        performance_moyenne = sum(performances) / len(performances) if performances else 0
        ecart_predictif_moyen = sum(ecarts) / len(ecarts) if ecarts else 0

        # Stratégie la plus efficace
        strategies_stats = {}
        for r in resultats:
            if r.statut in ['SUCCÈS', 'ÉCHEC'] and r.strategie_utilisee != 'DEFAUT':
                if r.strategie_utilisee not in strategies_stats:
                    strategies_stats[r.strategie_utilisee] = {'succes': 0, 'total': 0}
                strategies_stats[r.strategie_utilisee]['total'] += 1
                if r.statut == 'SUCCÈS':
                    strategies_stats[r.strategie_utilisee]['succes'] += 1

        strategie_plus_efficace = ""
        meilleur_taux = 0
        for strategie, stats in strategies_stats.items():
            if stats['total'] >= 5:  # Minimum 5 utilisations
                taux = stats['succes'] / stats['total'] * 100
                if taux > meilleur_taux:
                    meilleur_taux = taux
                    strategie_plus_efficace = f"{strategie} ({taux:.1f}%)"

        nb_strategies_utilisees = len(strategies_stats)

    else:
        taux_succes = 0
        taux_echec = 0
        temps_moyen = 0
        nb_predictions_s = 0
        nb_predictions_o = 0
        taux_succes_s = 0
        taux_succes_o = 0
        performance_moyenne = 0
        ecart_predictif_moyen = 0
        strategie_plus_efficace = ""
        nb_strategies_utilisees = 0
    
    return StatistiquesGlobales(
        nb_predictions_total=nb_predictions_valides,
        nb_succes=nb_succes,
        nb_echecs=nb_echecs,
        nb_neutres=nb_neutres,
        nb_wait=nb_wait,
        nb_abstentions=nb_abstentions,
        taux_succes=taux_succes,
        taux_echec=taux_echec,
        temps_total=temps_total,
        temps_moyen_prediction=temps_moyen,
        # NOUVELLES STATISTIQUES RÉVOLUTIONNAIRES
        nb_predictions_s=nb_predictions_s,
        nb_predictions_o=nb_predictions_o,
        taux_succes_s=taux_succes_s,
        taux_succes_o=taux_succes_o,
        performance_moyenne=performance_moyenne,
        ecart_predictif_moyen=ecart_predictif_moyen,
        strategie_plus_efficace=strategie_plus_efficace,
        nb_strategies_utilisees=nb_strategies_utilisees
    ), resultats

def afficher_resultats(stats: StatistiquesGlobales, resultats: List[ResultatPrediction]):
    """Affiche les résultats du test massif RÉVOLUTIONNAIRE"""
    print(f"\n🏆 RÉSULTATS DU TEST MASSIF RÉVOLUTIONNAIRE")
    print("=" * 80)

    print(f"📊 STATISTIQUES GLOBALES:")
    print(f"   Prédictions totales: {stats.nb_predictions_total}")
    print(f"   Succès: {stats.nb_succes}")
    print(f"   Échecs: {stats.nb_echecs}")
    print(f"   Neutres (TIE réels): {stats.nb_neutres}")
    print(f"   WAIT: {stats.nb_wait}")
    print(f"   ABSTENTIONS: {stats.nb_abstentions}")

    print(f"\n📈 PERFORMANCE GLOBALE:")
    print(f"   Taux de succès: {stats.taux_succes:.1f}%")
    print(f"   Taux d'échec: {stats.taux_echec:.1f}%")
    print(f"   Prédictions décisives: {stats.nb_succes + stats.nb_echecs}")

    print(f"\n🚀 PERFORMANCE RÉVOLUTIONNAIRE S/O:")
    print(f"   Prédictions CONTINUATION (S): {stats.nb_predictions_s}")
    print(f"   Prédictions ALTERNANCE (O): {stats.nb_predictions_o}")
    print(f"   Taux succès CONTINUATION: {stats.taux_succes_s:.1f}%")
    print(f"   Taux succès ALTERNANCE: {stats.taux_succes_o:.1f}%")

    print(f"\n🎯 MÉTRIQUES RÉVOLUTIONNAIRES:")
    print(f"   Performance théorique moyenne: {stats.performance_moyenne:.1f}%")
    print(f"   Écart prédictif moyen: +{stats.ecart_predictif_moyen:.1f} points")
    print(f"   Stratégies différentes utilisées: {stats.nb_strategies_utilisees}")
    print(f"   Stratégie la plus efficace: {stats.strategie_plus_efficace}")

    print(f"\n⏱️ TEMPS:")
    print(f"   Temps total: {stats.temps_total:.1f}s")
    print(f"   Temps moyen/prédiction: {stats.temps_moyen_prediction:.3f}s")
    
    # Analyse par confiance
    print(f"\n🔍 ANALYSE PAR NIVEAU DE CONFIANCE:")
    niveaux_confiance = {}
    for resultat in resultats:
        if resultat.statut in ['SUCCÈS', 'ÉCHEC']:
            niveau = resultat.confiance
            if niveau not in niveaux_confiance:
                niveaux_confiance[niveau] = {'succes': 0, 'echecs': 0}

            if resultat.statut == 'SUCCÈS':
                niveaux_confiance[niveau]['succes'] += 1
            else:
                niveaux_confiance[niveau]['echecs'] += 1

    for niveau, stats_niveau in niveaux_confiance.items():
        total = stats_niveau['succes'] + stats_niveau['echecs']
        taux = (stats_niveau['succes'] / total * 100) if total > 0 else 0
        print(f"   {niveau}: {stats_niveau['succes']}/{total} ({taux:.1f}%)")

    # NOUVELLE ANALYSE RÉVOLUTIONNAIRE DES STRATÉGIES
    print(f"\n🎯 ANALYSE DÉTAILLÉE DES STRATÉGIES RÉVOLUTIONNAIRES:")
    strategies_stats = {}
    for resultat in resultats:
        if resultat.statut in ['SUCCÈS', 'ÉCHEC'] and resultat.strategie_utilisee != 'DEFAUT':
            strategie = resultat.strategie_utilisee
            if strategie not in strategies_stats:
                strategies_stats[strategie] = {
                    'succes': 0, 'echecs': 0, 'total': 0,
                    'performance_moyenne': 0, 'performances': []
                }

            strategies_stats[strategie]['total'] += 1
            strategies_stats[strategie]['performances'].append(resultat.performance_theorique)

            if resultat.statut == 'SUCCÈS':
                strategies_stats[strategie]['succes'] += 1
            else:
                strategies_stats[strategie]['echecs'] += 1

    # Calculer performance moyenne pour chaque stratégie
    for strategie, stats in strategies_stats.items():
        if stats['performances']:
            stats['performance_moyenne'] = sum(stats['performances']) / len(stats['performances'])

    # Afficher top 10 des stratégies les plus utilisées
    strategies_triees = sorted(strategies_stats.items(), key=lambda x: x[1]['total'], reverse=True)
    print(f"   TOP 10 STRATÉGIES LES PLUS UTILISÉES:")
    for i, (strategie, stats) in enumerate(strategies_triees[:10]):
        taux_reel = (stats['succes'] / stats['total'] * 100) if stats['total'] > 0 else 0
        print(f"   {i+1:2d}. {strategie[:50]}")
        print(f"       Utilisations: {stats['total']} | Succès: {taux_reel:.1f}% | Perf théorique: {stats['performance_moyenne']:.1f}%")

    # Analyse par niveau d'efficacité
    print(f"\n🔥 ANALYSE PAR NIVEAU D'EFFICACITÉ:")
    niveaux_efficacite = {}
    for resultat in resultats:
        if resultat.statut in ['SUCCÈS', 'ÉCHEC']:
            niveau = resultat.niveau_efficacite
            if niveau not in niveaux_efficacite:
                niveaux_efficacite[niveau] = {'succes': 0, 'echecs': 0}

            if resultat.statut == 'SUCCÈS':
                niveaux_efficacite[niveau]['succes'] += 1
            else:
                niveaux_efficacite[niveau]['echecs'] += 1

    for niveau, stats_niveau in niveaux_efficacite.items():
        total = stats_niveau['succes'] + stats_niveau['echecs']
        taux = (stats_niveau['succes'] / total * 100) if total > 0 else 0
        print(f"   {niveau}: {stats_niveau['succes']}/{total} ({taux:.1f}%)")

    # Analyse des justifications révolutionnaires
    print(f"\n💡 ANALYSE DES JUSTIFICATIONS RÉVOLUTIONNAIRES:")
    justifications_freq = {}
    for resultat in resultats:
        if resultat.statut in ['SUCCÈS', 'ÉCHEC'] and resultat.justification:
            # Extraire le nom de la stratégie de la justification
            if "RÉVOLUTIONNAIRE:" in resultat.justification:
                parts = resultat.justification.split(" - ")
                if len(parts) >= 2:
                    description = parts[1].split(" (")[0]  # Enlever la performance
                    if description not in justifications_freq:
                        justifications_freq[description] = {'succes': 0, 'total': 0}
                    justifications_freq[description]['total'] += 1
                    if resultat.statut == 'SUCCÈS':
                        justifications_freq[description]['succes'] += 1

    # Afficher top 5 des justifications
    justifications_triees = sorted(justifications_freq.items(), key=lambda x: x[1]['total'], reverse=True)
    print(f"   TOP 5 JUSTIFICATIONS LES PLUS FRÉQUENTES:")
    for i, (justification, stats) in enumerate(justifications_triees[:5]):
        taux = (stats['succes'] / stats['total'] * 100) if stats['total'] > 0 else 0
        print(f"   {i+1}. {justification}: {stats['succes']}/{stats['total']} ({taux:.1f}%)")

if __name__ == "__main__":
    print("🚀 TEST MASSIF PRÉDICTEUR BACCARAT RÉVOLUTIONNAIRE")
    print("=" * 80)
    print("🎯 Test sur 1,000 parties réelles - COMPARAISON MODES")
    print("🧠 Analyse des 24 stratégies conditionnelles multidimensionnelles")
    print("📊 Évaluation performance S/O (continuation vs alternance)")
    print("🔥 Métriques révolutionnaires : DIFF + diff_L4 + diff_L5")
    print("⚖️ TIE réels = NEUTRE (ni succès ni échec)")
    print("🏆 Performance record attendue : 68.9% pour S, 58.5% pour O")
    print("=" * 80)

    # Test 1: Mode normal (toutes les stratégies)
    print("\n" + "="*60)
    print("🔥 TEST 1: MODE NORMAL - TOUTES LES STRATÉGIES")
    print("="*60)
    resultats_normal = tester_predicteur_massif(1000, mode_strategies_elites=False)

    # Test 2: Mode stratégies élites
    print("\n" + "="*60)
    print("🏆 TEST 2: MODE STRATÉGIES ÉLITES")
    print("="*60)
    resultats_elites = tester_predicteur_massif(1000, mode_strategies_elites=True)
    
    # Affichage des résultats comparatifs
    print("\n" + "="*80)
    print("📊 COMPARAISON DES PERFORMANCES")
    print("="*80)

    if resultats_normal:
        print(f"\n🔥 MODE NORMAL:")
        print(f"   Taux de succès: {resultats_normal.taux_succes:.1f}%")
        print(f"   Performance S: {resultats_normal.taux_succes_s:.1f}%")
        print(f"   Performance O: {resultats_normal.taux_succes_o:.1f}%")
        print(f"   Stratégies utilisées: {resultats_normal.nb_strategies_utilisees}")

    if resultats_elites:
        print(f"\n🏆 MODE ÉLITES:")
        print(f"   Taux de succès: {resultats_elites.taux_succes:.1f}%")
        print(f"   Performance S: {resultats_elites.taux_succes_s:.1f}%")
        print(f"   Performance O: {resultats_elites.taux_succes_o:.1f}%")
        print(f"   Stratégies utilisées: {resultats_elites.nb_strategies_utilisees}")

    # Déterminer le meilleur mode
    if resultats_normal and resultats_elites:
        if resultats_elites.taux_succes > resultats_normal.taux_succes:
            print(f"\n🏆 GAGNANT: MODE ÉLITES (+{resultats_elites.taux_succes - resultats_normal.taux_succes:.1f} points)")
        else:
            print(f"\n🔥 GAGNANT: MODE NORMAL (+{resultats_normal.taux_succes - resultats_elites.taux_succes:.1f} points)")

    print("\n" + "=" * 80)
