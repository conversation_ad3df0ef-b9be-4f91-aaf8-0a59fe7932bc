#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Massif du Prédicteur sur 1000 Parties Réelles
Évalue la performance du prédicteur sans métriques DIFF

Auteur: Expert Statisticien
Date: 2025-06-23
"""

import sys
import os
import json
import time
from typing import List, Dict, Tuple
from dataclasses import dataclass

# Ajouter le répertoire courant au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

@dataclass
class ResultatPrediction:
    """Résultat d'une prédiction individuelle"""
    main_numero: int
    prediction: str
    resultat_reel: str
    statut: str  # 'SUCCÈS', 'ÉCHEC', 'NEUTRE' (TIE), 'WAIT', 'ABSTENTION'
    probabilite: float
    confiance: str
    nb_simulations: int
    temps_calcul: float

@dataclass
class StatistiquesGlobales:
    """Statistiques globales du test"""
    nb_predictions_total: int
    nb_succes: int
    nb_echecs: int
    nb_neutres: int  # TIE réels
    nb_wait: int
    nb_abstentions: int
    taux_succes: float
    taux_echec: float
    temps_total: float
    temps_moyen_prediction: float

def charger_dataset_baccarat(fichier: str, nb_parties: int = 1000) -> List[Dict]:
    """
    Charge le dataset baccarat et retourne les premières parties

    Args:
        fichier: Chemin vers le fichier JSON
        nb_parties: Nombre de parties à charger

    Returns:
        List[Dict]: Liste des parties chargées
    """
    try:
        print(f"📂 Chargement du dataset: {fichier}")
        print(f"⚠️ Chargement de {nb_parties} parties (peut prendre du temps pour gros fichiers)")

        with open(fichier, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Structure identifiée: {"metadata": ..., "configuration": ..., "parties": [...]}
        if isinstance(data, dict) and 'parties' in data:
            parties = data['parties'][:nb_parties]
            print(f"✅ Format dict détecté - {len(data['parties'])} parties disponibles")
        elif isinstance(data, list):
            parties = data[:nb_parties]
            print(f"✅ Format liste détecté - {len(data)} parties disponibles")
        else:
            print(f"❌ Format de dataset non reconnu: {type(data)}")
            if isinstance(data, dict):
                print(f"   Clés disponibles: {list(data.keys())}")
            return []

        print(f"✅ {len(parties)} parties chargées pour le test")
        return parties

    except FileNotFoundError:
        print(f"❌ Fichier non trouvé: {fichier}")
        return []
    except json.JSONDecodeError as e:
        print(f"❌ Erreur JSON: {e}")
        return []
    except MemoryError:
        print(f"❌ Erreur mémoire - Fichier trop volumineux")
        print(f"💡 Essayez avec un nombre de parties plus petit")
        return []
    except Exception as e:
        print(f"❌ Erreur chargement: {e}")
        return []

def convertir_partie_vers_etat(partie: Dict, main_actuelle: int):
    """
    Convertit une partie du dataset vers un état prédicteur

    Args:
        partie: Partie du dataset
        main_actuelle: Numéro de la main actuelle

    Returns:
        EtatPartieActuel: État pour le prédicteur
    """
    from predicteur_temps_reel_ultra import EtatPartieActuel

    # Extraire les mains jusqu'à main_actuelle
    mains = partie.get('mains', [])[:main_actuelle]

    if len(mains) < 6:  # Minimum pour calculs L4/L5
        return None

    # Convertir vers INDEX5 et INDEX1 selon la structure identifiée
    sequence_index5 = []
    sequence_index1 = []

    for main in mains:
        # Gestion flexible de différents formats de dataset
        index5_value = None
        index1_value = None

        # Format 1: Structure du fichier test (index5_combined, index3_result)
        if 'index5_combined' in main:
            index5_value = main['index5_combined']
        elif 'index5' in main:
            index5_value = main['index5']

        if 'index3_result' in main:
            index1_value = main['index3_result']
        elif 'resultat' in main:
            index1_value = main['resultat']
        elif 'index1' in main:
            index1_value = main['index1']
        elif 'result' in main:
            index1_value = main['result']

        # Ajouter seulement si les deux valeurs sont trouvées
        if index5_value and index1_value:
            sequence_index5.append(index5_value)
            sequence_index1.append(index1_value)

    if len(sequence_index5) != len(sequence_index1) or len(sequence_index5) == 0:
        return None

    # UTILISER LES CAPACITÉS INTÉGRÉES DU PRÉDICTEUR
    # Le prédicteur calcule lui-même les ratios, on donne des valeurs par défaut
    return EtatPartieActuel(
        sequence_index5=sequence_index5,
        sequence_index1=sequence_index1,
        main_actuelle=len(sequence_index5),
        ratio_l4_actuel=0.5,  # Valeur par défaut - le prédicteur recalculera
        ratio_l5_actuel=0.6,  # Valeur par défaut - le prédicteur recalculera
        ratio_l4_precedent=0.4,  # Valeur par défaut
        ratio_l5_precedent=0.5   # Valeur par défaut
    )

def evaluer_prediction(prediction: str, resultat_reel: str) -> str:
    """
    Évalue une prédiction selon les règles définies avec validation stratégique

    Args:
        prediction: Prédiction du système
        resultat_reel: Résultat réel de la main (BANKER/PLAYER/TIE)

    Returns:
        str: 'SUCCÈS', 'ÉCHEC', 'NEUTRE', 'WAIT', 'ABSTENTION'
    """
    # WAIT est maintenant neutre (ni succès ni échec) selon les nouvelles règles
    if prediction == 'WAIT':
        return 'NEUTRE'  # Changé de 'WAIT' à 'NEUTRE'

    if prediction == 'ABSTENTION':
        return 'NEUTRE'  # Changé de 'ABSTENTION' à 'NEUTRE'

    # Si le résultat réel est TIE → NEUTRE (ni succès ni échec)
    if resultat_reel == 'TIE':
        return 'NEUTRE'

    # Comparaison directe pour BANKER/PLAYER
    # Les valeurs possibles identifiées sont: "BANKER", "PLAYER", "TIE"
    if prediction == resultat_reel:
        return 'SUCCÈS'
    else:
        return 'ÉCHEC'

def tester_predicteur_massif(nb_parties: int = 1000) -> StatistiquesGlobales:
    """
    Test massif du prédicteur sur nb_parties
    
    Args:
        nb_parties: Nombre de parties à tester
        
    Returns:
        StatistiquesGlobales: Résultats du test
    """
    print(f"🚀 DÉBUT TEST MASSIF SUR {nb_parties} PARTIES")
    print("=" * 70)
    
    # Charger le dataset principal (utiliser toutes les parties disponibles)
    parties = charger_dataset_baccarat('dataset_baccarat_lupasco_20250623_080828.json', nb_parties)
    if not parties:
        print("❌ Impossible de charger le dataset")
        return None
    
    # Initialiser le prédicteur
    try:
        from predicteur_temps_reel_ultra import PredicteurTempsReelUltra
        predicteur = PredicteurTempsReelUltra()
        print("✅ Prédicteur initialisé")
    except Exception as e:
        print(f"❌ Erreur initialisation prédicteur: {e}")
        return None
    
    # Variables de suivi
    resultats = []
    temps_debut = time.time()
    
    nb_succes = 0
    nb_echecs = 0
    nb_neutres = 0
    nb_wait = 0
    nb_abstentions = 0
    nb_predictions_valides = 0
    
    print(f"\n📊 TRAITEMENT DES PARTIES:")
    
    for i, partie in enumerate(parties):
        try:
            # Prendre une main au milieu de la partie pour avoir assez d'historique
            mains_partie = partie.get('mains', [])
            if len(mains_partie) < 10:  # Minimum pour test valide
                continue
            
            # Tester sur la main 8 (index 7) pour prédire la main 9
            main_test = 8
            if len(mains_partie) <= main_test:
                continue
            
            # Convertir vers état prédicteur
            etat = convertir_partie_vers_etat(partie, main_test)
            if etat is None:
                continue
            
            # Obtenir le résultat réel de la main suivante (gestion flexible)
            main_suivante = mains_partie[main_test]
            resultat_reel = None

            # Essayer différents noms de champs
            for champ in ['index3_result', 'resultat', 'result', 'index1']:
                if champ in main_suivante:
                    resultat_reel = main_suivante[champ]
                    break

            if not resultat_reel or resultat_reel == 'UNKNOWN':
                continue
            
            # Effectuer la prédiction
            temps_pred_debut = time.time()
            try:
                prediction_obj = predicteur.predire_main_suivante(etat)
                temps_pred = time.time() - temps_pred_debut
                
                # Évaluer la prédiction
                statut = evaluer_prediction(prediction_obj.prediction_finale, resultat_reel)
                
                # Compter les résultats (WAIT et ABSTENTION sont maintenant NEUTRE)
                if statut == 'SUCCÈS':
                    nb_succes += 1
                elif statut == 'ÉCHEC':
                    nb_echecs += 1
                elif statut == 'NEUTRE':
                    nb_neutres += 1
                    # Distinguer les types de neutres pour information
                    if prediction_obj.prediction_finale == 'WAIT':
                        nb_wait += 1
                    elif prediction_obj.prediction_finale == 'ABSTENTION':
                        nb_abstentions += 1
                
                nb_predictions_valides += 1
                
                # Stocker le résultat
                resultats.append(ResultatPrediction(
                    main_numero=main_test + 1,
                    prediction=prediction_obj.prediction_finale,
                    resultat_reel=resultat_reel,
                    statut=statut,
                    probabilite=prediction_obj.probabilite,
                    confiance=prediction_obj.confiance,
                    nb_simulations=prediction_obj.nb_simulations_total,
                    temps_calcul=temps_pred
                ))
                
                # Affichage périodique
                if (i + 1) % 100 == 0:
                    print(f"   Partie {i+1:4d}/{nb_parties} - Prédictions valides: {nb_predictions_valides}")
                
            except Exception as e:
                print(f"⚠️ Erreur prédiction partie {i+1}: {e}")
                continue
                
        except Exception as e:
            print(f"⚠️ Erreur traitement partie {i+1}: {e}")
            continue
    
    temps_total = time.time() - temps_debut
    
    # Calculer les statistiques
    if nb_predictions_valides > 0:
        # Calculer taux sur prédictions non-neutres uniquement
        nb_predictions_decisives = nb_succes + nb_echecs
        taux_succes = (nb_succes / nb_predictions_decisives * 100) if nb_predictions_decisives > 0 else 0
        taux_echec = (nb_echecs / nb_predictions_decisives * 100) if nb_predictions_decisives > 0 else 0
        temps_moyen = temps_total / nb_predictions_valides
    else:
        taux_succes = 0
        taux_echec = 0
        temps_moyen = 0
    
    return StatistiquesGlobales(
        nb_predictions_total=nb_predictions_valides,
        nb_succes=nb_succes,
        nb_echecs=nb_echecs,
        nb_neutres=nb_neutres,
        nb_wait=nb_wait,
        nb_abstentions=nb_abstentions,
        taux_succes=taux_succes,
        taux_echec=taux_echec,
        temps_total=temps_total,
        temps_moyen_prediction=temps_moyen
    ), resultats

def afficher_resultats(stats: StatistiquesGlobales, resultats: List[ResultatPrediction]):
    """Affiche les résultats du test massif"""
    print(f"\n🏆 RÉSULTATS DU TEST MASSIF")
    print("=" * 70)
    
    print(f"📊 STATISTIQUES GLOBALES:")
    print(f"   Prédictions totales: {stats.nb_predictions_total}")
    print(f"   Succès: {stats.nb_succes}")
    print(f"   Échecs: {stats.nb_echecs}")
    print(f"   Neutres (TIE réels): {stats.nb_neutres}")
    print(f"   WAIT: {stats.nb_wait}")
    print(f"   ABSTENTIONS: {stats.nb_abstentions}")
    
    print(f"\n📈 PERFORMANCE:")
    print(f"   Taux de succès: {stats.taux_succes:.1f}%")
    print(f"   Taux d'échec: {stats.taux_echec:.1f}%")
    print(f"   Prédictions décisives: {stats.nb_succes + stats.nb_echecs}")
    
    print(f"\n⏱️ TEMPS:")
    print(f"   Temps total: {stats.temps_total:.1f}s")
    print(f"   Temps moyen/prédiction: {stats.temps_moyen_prediction:.3f}s")
    
    # Analyse par confiance
    print(f"\n🔍 ANALYSE PAR NIVEAU DE CONFIANCE:")
    niveaux_confiance = {}
    for resultat in resultats:
        if resultat.statut in ['SUCCÈS', 'ÉCHEC']:
            niveau = resultat.confiance
            if niveau not in niveaux_confiance:
                niveaux_confiance[niveau] = {'succes': 0, 'echecs': 0}
            
            if resultat.statut == 'SUCCÈS':
                niveaux_confiance[niveau]['succes'] += 1
            else:
                niveaux_confiance[niveau]['echecs'] += 1
    
    for niveau, stats_niveau in niveaux_confiance.items():
        total = stats_niveau['succes'] + stats_niveau['echecs']
        taux = (stats_niveau['succes'] / total * 100) if total > 0 else 0
        print(f"   {niveau}: {stats_niveau['succes']}/{total} ({taux:.1f}%)")

if __name__ == "__main__":
    print("🚀 TEST MASSIF PRÉDICTEUR BACCARAT")
    print("=" * 70)
    print("🎯 Test sur 10,000 parties réelles")
    print("📊 Évaluation avec validation conditionnelle tableau prédictif")
    print("⚖️ TIE réels = NEUTRE (ni succès ni échec)")
    print("=" * 70)

    # Lancer le test
    resultats_test = tester_predicteur_massif(10000)
    
    if resultats_test:
        stats, resultats = resultats_test
        afficher_resultats(stats, resultats)
        
        # Sauvegarder les résultats détaillés
        nom_fichier = f"resultats_test_massif_{int(time.time())}.json"
        try:
            with open(nom_fichier, 'w', encoding='utf-8') as f:
                json.dump({
                    'statistiques': {
                        'nb_predictions_total': stats.nb_predictions_total,
                        'nb_succes': stats.nb_succes,
                        'nb_echecs': stats.nb_echecs,
                        'nb_neutres': stats.nb_neutres,
                        'nb_wait': stats.nb_wait,
                        'nb_abstentions': stats.nb_abstentions,
                        'taux_succes': stats.taux_succes,
                        'taux_echec': stats.taux_echec,
                        'temps_total': stats.temps_total,
                        'temps_moyen_prediction': stats.temps_moyen_prediction
                    },
                    'resultats_detailles': [
                        {
                            'main_numero': r.main_numero,
                            'prediction': r.prediction,
                            'resultat_reel': r.resultat_reel,
                            'statut': r.statut,
                            'probabilite': r.probabilite,
                            'confiance': r.confiance,
                            'nb_simulations': r.nb_simulations,
                            'temps_calcul': r.temps_calcul
                        } for r in resultats
                    ]
                }, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Résultats sauvegardés: {nom_fichier}")
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde: {e}")
    else:
        print("❌ Test échoué")
    
    print("\n" + "=" * 70)
